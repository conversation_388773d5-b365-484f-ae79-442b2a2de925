import React, { useRef, useEffect } from 'react';
import * as maptilersdk from '@maptiler/sdk';
import "@maptiler/sdk/dist/maptiler-sdk.css";

maptilersdk.config.apiKey = 'a7S8ykFBG9qI73UllxnL';

interface TrackPoint {
  lat: number;
  lng: number;
  timestamp: string;
  speed?: number;
}

interface SimpleRouteMapProps {
  trackData: TrackPoint[];
  height?: string;
}

export default function SimpleRouteMap({
  trackData,
  height = '500px',
}: SimpleRouteMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maptilersdk.Map | null>(null);

  useEffect(() => {
    if (map.current || !mapContainer.current || trackData.length === 0) return;

    const initialCenter = [trackData[0].lng, trackData[0].lat];

    try {
      map.current = new maptilersdk.Map({
        container: mapContainer.current,
        style: maptilersdk.MapStyle.STREETS,
        center: initialCenter,
        zoom: 12,
      });

      map.current.on('load', () => {
        console.log('Simple route map loaded successfully');
        addRouteToMap();
      });

      map.current.on('error', (e) => {
        console.error('Simple route map error:', e);
      });

    } catch (error) {
      console.error('Error initializing simple route map:', error);
    }

  }, [trackData]);

  const addRouteToMap = () => {
    if (!map.current || trackData.length === 0) return;

    try {
      // Create simple line from track points
      const coordinates = trackData.map(point => [point.lng, point.lat]);

      // Add route source
      map.current.addSource('route', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: coordinates
          }
        }
      });

      // Add route layer
      map.current.addLayer({
        id: 'route-line',
        type: 'line',
        source: 'route',
        paint: {
          'line-color': '#00D4AA',
          'line-width': 4,
          'line-opacity': 0.8
        }
      });

      // Add start marker
      new maptilersdk.Marker({
        color: "#22C55E",
        scale: 1.2
      })
        .setLngLat([trackData[0].lng, trackData[0].lat])
        .setPopup(new maptilersdk.Popup().setHTML('<div><strong>Start Point</strong></div>'))
        .addTo(map.current);

      // Add end marker
      if (trackData.length > 1) {
        const lastPoint = trackData[trackData.length - 1];
        new maptilersdk.Marker({
          color: "#EF4444",
          scale: 1.2
        })
          .setLngLat([lastPoint.lng, lastPoint.lat])
          .setPopup(new maptilersdk.Popup().setHTML('<div><strong>End Point</strong></div>'))
          .addTo(map.current);
      }

      // Fit bounds to show entire route
      const bounds = new maptilersdk.LngLatBounds();
      coordinates.forEach(coord => bounds.extend(coord));
      map.current.fitBounds(bounds, { padding: 50 });

    } catch (error) {
      console.error('Error adding route to map:', error);
    }
  };

  return (
    <div 
      ref={mapContainer} 
      style={{ height }} 
      className="w-full rounded-lg"
    />
  );
}
