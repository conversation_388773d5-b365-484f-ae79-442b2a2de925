import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer } from 'recharts';

interface LaporanChartProps {
  data: Array<{
    noESeal: string;
    mileage: string;
  }>;
}

const LaporanChart: React.FC<LaporanChartProps> = ({ data }) => {
  // Transform data for chart
  const chartData = data.map(item => ({
    name: item.noESeal,
    mileage: parseFloat(item.mileage) || 0,
  }));

  return (
    <div className="w-full">
      <div className="mb-4">
        <h4 className="text-base font-medium text-gray-700">Laporan</h4>
        <div className="flex items-center gap-4 mt-1">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Unit: Km</span>
          </div>
        </div>
      </div>

      <div style={{ width: '100%', height: '320px' }}>
        <ResponsiveContainer width="100%" height="100%">
          <Bar<PERSON>hart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              label={{ value: '30', angle: 0, position: 'outside' }}
              domain={[0, 30]}
              ticks={[0, 5, 10, 15, 20, 25, 30]}
            />
            <Bar
              dataKey="mileage"
              fill="#8b5cf6"
              name="E-Seal"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-3 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-500 rounded"></div>
          <span className="text-sm text-gray-600">E-Seal</span>
        </div>
      </div>
    </div>
  );
};

export default LaporanChart;
