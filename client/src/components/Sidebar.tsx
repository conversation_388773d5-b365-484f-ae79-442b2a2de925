import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, User } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useSession, signOut } from '@/lib/auth-client';

interface SidebarProps {
  activeMenu: string;
  onMenuClick: (menu: string) => void;
  isMobile?: boolean;
}

export function Sidebar({ activeMenu, onMenuClick, isMobile = false }: SidebarProps) {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const [isPengaturanOpen, setIsPengaturanOpen] = useState(false);
  const { data: session } = useSession();
  const userRole = session?.user?.role;

  const handleLogout = async () => {
    try {
      await signOut();
      // The ProtectedRoute component will automatically handle the redirect
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const allMenuItems = {
    superadmin: [
      { id: 'tambah-data', label: 'Tambah Data', parent: 'data-eseal-group', path: '/superadmin/tambah-data' },
      { id: 'dokumen-kepabean', label: 'Dokumen Kepabean', parent: 'main', path: '/superadmin/dokumen-kepabean' },
      { id: 'start-stop', label: 'Start - Stop', parent: 'tracking-data-group', path: '/superadmin/start-stop' },
      { id: 'status', label: 'Status', parent: 'tracking-data-group', path: '/superadmin/status' },
      { id: 'monitoring', label: 'Monitoring', parent: 'tracking-data-group', path: '/superadmin/monitoring' },
      { id: 'laporan', label: 'Laporan', parent: 'main', path: '/superadmin/laporan' },
      { id: 'logs', label: 'Logs', parent: 'main', path: '/superadmin/logs' },
      { id: 'manajemen-organisasi', label: 'Manajemen Organisasi', parent: 'pengaturan-sistem', path: '/superadmin/manajemen-organisasi' },
      { id: 'manajemen-pengguna', label: 'Manajemen Pengguna', parent: 'pengaturan-sistem', path: '/superadmin/manajemen-pengguna' },
      { id: 'role-management', label: 'Role Management', parent: 'pengaturan-sistem', path: '/superadmin/role-management' }
    ],
    admin: [
      { id: 'data-eseal', label: 'Tambah Data', parent: 'data-eseal-group', path: `/admin/${slug}/data-eseal` },
      { id: 'geofence', label: 'Geofence', parent: 'data-eseal-group', path: `/admin/${slug}/geofence` },
      { id: 'dokumen-kepabean', label: 'Dokumen Kepabean', parent: 'main', path: `/admin/${slug}/dokumen-kepabean` },
      { id: 'start-stop', label: 'Start - Stop', parent: 'tracking-data-group', path: `/admin/${slug}/start-stop` },
      { id: 'status', label: 'Status', parent: 'tracking-data-group', path: `/admin/${slug}/status` },
      { id: 'monitoring', label: 'Monitoring', parent: 'tracking-data-group', path: `/admin/${slug}/monitoring` },
      { id: 'laporan', label: 'Laporan', parent: 'main', path: `/admin/${slug}/laporan` },
      { id: 'logs-admin', label: 'Logs', parent: 'main', path: `/admin/${slug}/logs` }
    ]
  };

  const menuItems = userRole ? allMenuItems[userRole as keyof typeof allMenuItems] || [] : [];

  const [openMenus, setOpenMenus] = useState<string[]>([]);

  useEffect(() => {
    const activeParent = menuItems.find(item => item.id === activeMenu)?.parent;
    if (activeParent && !openMenus.includes(activeParent)) {
      setOpenMenus(prev => [...prev, activeParent]);
    }
  }, [activeMenu, menuItems]);

  const toggleMenu = (menuId: string) => {
    setOpenMenus(prev => prev.includes(menuId) ? prev.filter(id => id !== menuId) : [...prev, menuId]);
  };

  const sidebarClasses = isMobile
    ? 'w-full text-white h-full block'
    : 'w-64 bg-slate-800 text-white rounded-xl shadow-lg p-4 h-full flex flex-col';

  return (
    <div className={sidebarClasses}>
      {/* Header Content for Mobile */}
      {isMobile && (
        <div className="mb-6 pb-4 border-b border-slate-600">
          {/* Logo */}
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-white font-bold text-sm">VIT</span>
            </div>
            <div>
              <div className="text-sm font-medium text-white">VIT-PLI</div>
              <div className="text-xs text-slate-300">Vessel Tracking & Monitoring</div>
            </div>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 text-white hover:bg-slate-700 w-full justify-start">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
                <span className="text-sm font-medium">{session?.user?.name || 'User'}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuItem>Profile</DropdownMenuItem>
              <DropdownMenuItem>Settings</DropdownMenuItem>
              <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      <div className="flex-1 overflow-y-auto">
        {userRole === 'admin' && (
          <>
            {/* 1. Data E-Seal Group */}
            <div className="mb-4">
              <button onClick={() => toggleMenu('data-eseal-group')} className="flex items-center justify-between w-full p-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                <span className="font-medium">Data E-Seal</span>
                {openMenus.includes('data-eseal-group') ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </button>
              {openMenus.includes('data-eseal-group') && (
                <div className="ml-4 mt-1 space-y-1">
                  {menuItems.filter(item => item.parent === 'data-eseal-group').map(item => (
                    <button key={item.id} onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-2 rounded transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700'}`}>
                      {item.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 2. Dokumen Kepabean - menu tunggal */}
            <div className="mb-4">
              <button onClick={() => { onMenuClick('dokumen-kepabean'); navigate(`/admin/${slug}/dokumen-kepabean`); }} className={`w-full text-left p-3 rounded-lg transition-colors ${activeMenu === 'dokumen-kepabean' ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`}>
                Dokumen Kepabean
              </button>
            </div>

            {/* 3. Tracking Data Group */}
            <div className="mb-4">
              <button onClick={() => toggleMenu('tracking-data-group')} className="flex items-center justify-between w-full p-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                <span className="font-medium">Tracking Data</span>
                {openMenus.includes('tracking-data-group') ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </button>
              {openMenus.includes('tracking-data-group') && (
                <div className="ml-4 mt-1 space-y-1">
                  {menuItems.filter(item => item.parent === 'tracking-data-group').map(item => (
                    <button key={item.id} onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-2 rounded transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700'}`}>
                      {item.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 4. Main items lainnya (Laporan, Logs) */}
            {menuItems.filter(item => item.parent === 'main' && item.id !== 'dokumen-kepabean').map(item => (
              <div className="mb-4" key={item.id}>
                <button onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-3 rounded-lg transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`}>
                  {item.label}
                </button>
              </div>
            ))}
          </>
        )}

        {/* Superadmin specific - dalam urutan yang benar */}
        {userRole === 'superadmin' && (
          <>
            {/* 1. Data E-Seal Group */}
            <div className="mb-4">
              <button onClick={() => toggleMenu('data-eseal-group')} className="flex items-center justify-between w-full p-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                <span className="font-medium">Data E-Seal</span>
                {openMenus.includes('data-eseal-group') ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </button>
              {openMenus.includes('data-eseal-group') && (
                <div className="ml-4 mt-1 space-y-1">
                  {menuItems.filter(item => item.parent === 'data-eseal-group').map(item => (
                    <button key={item.id} onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-2 rounded transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700'}`}>
                      {item.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 2. Dokumen Kepabean - menu tunggal */}
            <div className="mb-4">
              <button onClick={() => { onMenuClick('dokumen-kepabean'); navigate('/superadmin/dokumen-kepabean'); }} className={`w-full text-left p-3 rounded-lg transition-colors ${activeMenu === 'dokumen-kepabean' ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`}>
                Dokumen Kepabean
              </button>
            </div>

            {/* 3. Tracking Data Group */}
            <div className="mb-4">
              <button onClick={() => toggleMenu('tracking-data-group')} className="flex items-center justify-between w-full p-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                <span className="font-medium">Tracking Data</span>
                {openMenus.includes('tracking-data-group') ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </button>
              {openMenus.includes('tracking-data-group') && (
                <div className="ml-4 mt-1 space-y-1">
                  {menuItems.filter(item => item.parent === 'tracking-data-group').map(item => (
                    <button key={item.id} onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-2 rounded transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700'}`}>
                      {item.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 4. Laporan - menu tunggal */}
            <div className="mb-4">
              <button onClick={() => { onMenuClick('laporan'); navigate('/superadmin/laporan'); }} className={`w-full text-left p-3 rounded-lg transition-colors ${activeMenu === 'laporan' ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`}>
                Laporan
              </button>
            </div>

            {/* 5. Logs - menu tunggal */}
            <div className="mb-4">
              <button onClick={() => { onMenuClick('logs'); navigate('/superadmin/logs'); }} className={`w-full text-left p-3 rounded-lg transition-colors ${activeMenu === 'logs' ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`}>
                Logs
              </button>
            </div>

            {/* 6. Pengaturan Sistem Group */}
            <div className="mb-4">
              <button onClick={() => toggleMenu('pengaturan-sistem')} className="flex items-center justify-between w-full p-3 bg-slate-700 rounded-lg mb-2 hover:bg-slate-600 transition-colors">
                <span className="font-medium">Pengaturan Sistem</span>
                {openMenus.includes('pengaturan-sistem') ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </button>
              {openMenus.includes('pengaturan-sistem') && (
                <div className="ml-4 space-y-1">
                  {menuItems.filter(item => item.parent === 'pengaturan-sistem').map(item => (
                    <button key={item.id} onClick={() => { onMenuClick(item.id); navigate(item.path); }} className={`w-full text-left p-2 rounded transition-colors ${activeMenu === item.id ? 'bg-amber-100 text-amber-900' : 'text-slate-300 hover:bg-slate-700'}`}>
                      {item.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
