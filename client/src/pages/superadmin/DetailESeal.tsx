import { useState, useEffect } from 'react';
import { ArrowLeft, Shield, Smartphone, Building, Tag, Info } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';

interface DetailESealProps {
  esealId: string;
  onBack: () => void;
}

interface ESealMasterData {
  id: string;
  noEseal: string;
  noImei: string;
  vendor: string;
  tipe: string;
  status: 'AKTIF' | 'TIDAK_AKTIF' | 'INACTIVE';
}

// Dummy master data untuk E-Seal
const dummyESealMasterData: ESealMasterData = {
  id: '1',
  noEseal: 'E-Seal 11198271 - 1',
  noImei: '11198271',
  vendor: '373c041c-f9b8-491f-a4f6-7cc47a0569d3',
  tipe: 'BOLT_SEAL',
  status: 'AKTIF'
};

export default function DetailESeal({ esealId, onBack }: DetailESealProps) {
  const [esealDetail, setESealDetail] = useState<ESealMasterData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const fetchESealDetail = async () => {
      setLoading(true);
      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setESealDetail(dummyESealMasterData);
      setLoading(false);
    };

    fetchESealDetail();
  }, [esealId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-600"></div>
      </div>
    );
  }

  if (!esealDetail) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <p className="text-gray-500 mb-4">E-Seal tidak ditemukan</p>
          <Button onClick={onBack} variant="outline" className="border-slate-300 text-slate-700 hover:bg-slate-50">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AKTIF':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'TIDAK_AKTIF':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center hover:bg-slate-100 text-slate-700"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-2xl font-bold text-slate-900">
                Detail E-Seal
              </h1>
              <p className="text-sm text-slate-600 mt-1">
                Informasi lengkap E-Seal {esealDetail.noEseal}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information Card */}
          <div className="lg:col-span-2">
            <Card className="shadow-sm">
              <CardHeader className="bg-slate-800 text-white">
                <CardTitle className="flex items-center text-lg">
                  <Shield className="w-5 h-5 mr-2" />
                  Informasi E-Seal
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Nomor E-Seal */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-slate-500" />
                      Nomor E-Seal
                    </label>
                    <div className="bg-slate-50 px-4 py-3 rounded-lg border border-slate-200">
                      <p className="text-base font-semibold text-slate-900 break-all">
                        {esealDetail.noEseal}
                      </p>
                    </div>
                  </div>

                  {/* Nomor IMEI */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 flex items-center">
                      <Smartphone className="w-4 h-4 mr-2 text-slate-500" />
                      Nomor IMEI
                    </label>
                    <div className="bg-slate-50 px-4 py-3 rounded-lg border border-slate-200">
                      <p className="text-base font-mono text-slate-900 break-all">
                        {esealDetail.noImei}
                      </p>
                    </div>
                  </div>

                  {/* Vendor */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 flex items-center">
                      <Building className="w-4 h-4 mr-2 text-slate-500" />
                      Vendor
                    </label>
                    <div className="bg-slate-50 px-4 py-3 rounded-lg border border-slate-200">
                      <p className="text-base text-slate-900 break-all">
                        {esealDetail.vendor}
                      </p>
                    </div>
                  </div>

                  {/* Tipe */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 flex items-center">
                      <Tag className="w-4 h-4 mr-2 text-slate-500" />
                      Tipe E-Seal
                    </label>
                    <div className="bg-slate-50 px-4 py-3 rounded-lg border border-slate-200">
                      <p className="text-base text-slate-900">
                        {esealDetail.tipe}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Card */}
          <div className="lg:col-span-1">
            <Card className="shadow-sm">
              <CardHeader className="bg-slate-800 text-white">
                <CardTitle className="flex items-center text-lg">
                  <Info className="w-5 h-5 mr-2" />
                  Status
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-slate-700 block mb-2">
                      Status Operasional
                    </label>
                    <Badge
                      className={`px-3 py-2 text-sm font-medium ${getStatusColor(esealDetail.status)}`}
                    >
                      {esealDetail.status}
                    </Badge>
                  </div>

                  <div className="pt-4 border-t border-slate-200">
                    <p className="text-xs text-slate-500 leading-relaxed">
                      Status ini menunjukkan kondisi fisik dan operasional E-Seal saat ini.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
