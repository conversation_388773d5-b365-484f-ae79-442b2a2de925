import { useState } from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';

// Dummy data sesuai dengan design yang diminta
const dummyLogsData = [
  {
    id: 1,
    no: 1,
    idVendor: '884GKEL637',
    nomorESeal: '7848585252',
    nomorAju: '123',
    waktuStart: '29-06-2025 14:22:31',
    waktuStop: '5-07-2025 06:03:55',
  },
  {
    id: 2,
    no: 2,
    idVendor: '521AVL2525',
    nomorESeal: '8052505751',
    nomorAju: '567',
    waktuStart: '10-07-2025 21:00:26',
    waktuStop: '-',
  },
];

export default function Logs() {
  const [searchTerm, setSearchTerm] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data berdasarkan search term
  const filteredData = dummyLogsData.filter(item =>
    item.idVendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.nomorESeal.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.nomorAju.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / parseInt(entriesPerPage));
  const startIndex = (currentPage - 1) * parseInt(entriesPerPage);
  const endIndex = Math.min(startIndex + parseInt(entriesPerPage), totalEntries);
  const currentData = filteredData.slice(startIndex, endIndex);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Logs</h1>
        <div className="h-1 w-20 bg-slate-800 rounded"></div>
      </div>

      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-16">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per pages</span>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Cari</span>
          <Input
            placeholder=""
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">ID Vendor</TableHead>
                <TableHead className="font-semibold text-white">Nomor E-Seal</TableHead>
                <TableHead className="font-semibold text-white">Nomor Aju</TableHead>
                <TableHead className="font-semibold text-white">Waktu Start</TableHead>
                <TableHead className="font-semibold text-white">Waktu Stop</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    Data Tidak Ditemukan
                  </TableCell>
                </TableRow>
              ) : (
                currentData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-mono text-sm">{item.idVendor}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorESeal}</TableCell>
                    <TableCell className="text-center">{item.nomorAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.waktuStart}</TableCell>
                    <TableCell className="font-mono text-sm">{item.waktuStop}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {totalEntries > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Menampilkan {startIndex + 1} sampai {endIndex} dari {totalEntries} entri
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(p - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(p + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}