import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../components/ui/dialog';
import { Plus, Search, Edit, Trash2 } from 'lucide-react';
import MapTiler from '../../../components/map/MapTiler';

interface GeofenceData {
  id: string;
  no: number;
  nama: string;
  tipeArea: string;
  alamat: string;
  luasArea: string;
  geometri: string;
}

const Geofence: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [search, setSearch] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [namaGeofence, setNamaGeofence] = useState('');
  const [tipeArea, setTipeArea] = useState('');

  // Dummy data for geofence
  const [geofenceData, setGeofenceData] = useState<GeofenceData[]>([]);

  const filteredData = geofenceData.filter(item =>
    item.nama.toLowerCase().includes(search.toLowerCase()) ||
    item.alamat.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddGeofence = () => {
    if (!namaGeofence || !tipeArea) {
      alert('Nama Geofence dan Tipe Area harus diisi!');
      return;
    }

    const newGeofence: GeofenceData = {
      id: Date.now().toString(),
      no: geofenceData.length + 1,
      nama: namaGeofence,
      tipeArea: tipeArea,
      alamat: 'Alun-Alun Kendal, Jawa Tengah', // Default from map
      luasArea: '2.5 km²',
      geometri: 'Polygon'
    };

    setGeofenceData([...geofenceData, newGeofence]);
    setNamaGeofence('');
    setTipeArea('');
    setIsAddModalOpen(false);
  };

  const handleDeleteGeofence = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus geofence ini?')) {
      setGeofenceData(geofenceData.filter(item => item.id !== id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Geofence</h1>
          <p className="text-gray-600">Kelola area geofence untuk organisasi {slug}</p>
        </div>
        
        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
          <DialogTrigger asChild>
            <Button className="bg-slate-800 hover:bg-slate-700 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Tambah
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Tambah Data Geofence</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Nama Geofence <span className="text-red-500">*</span>
                  </label>
                  <Input
                    placeholder="Nama Geofence *"
                    value={namaGeofence}
                    onChange={(e) => setNamaGeofence(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Tipe Area <span className="text-red-500">*</span>
                  </label>
                  <Select value={tipeArea} onValueChange={setTipeArea}>
                    <SelectTrigger>
                      <SelectValue placeholder="-TIPE AREA *" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="circle">Circle</SelectItem>
                      <SelectItem value="polygon">Polygon</SelectItem>
                      <SelectItem value="rectangle">Rectangle</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Map */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Pilih Area di Peta
                </label>
                <div className="h-96 border rounded-lg overflow-hidden">
                  <MapTiler
                    originLocation={{
                      lat: -6.9175,
                      lng: 110.1625,
                      address: 'Alun-Alun Kendal, Jawa Tengah'
                    }}
                    interactive={true}
                    height="100%"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsAddModalOpen(false)}
                  className="flex-1"
                >
                  Batal
                </Button>
                <Button
                  onClick={handleAddGeofence}
                  className="flex-1 bg-slate-800 hover:bg-slate-700 text-white"
                >
                  Tambah
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Controls */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">10</span>
            <span className="text-sm text-gray-600">entries per pages</span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Cari</span>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari geofence..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Nama</TableHead>
                <TableHead className="font-semibold text-white">Tipe Area</TableHead>
                <TableHead className="font-semibold text-white">Alamat</TableHead>
                <TableHead className="font-semibold text-white">Luas Area</TableHead>
                <TableHead className="font-semibold text-white">Geometri</TableHead>
                <TableHead className="font-semibold text-white text-center">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    {geofenceData.length === 0 
                      ? 'Geofence masih kosong' 
                      : 'Tidak ada data yang sesuai dengan pencarian'
                    }
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-medium">{item.nama}</TableCell>
                    <TableCell>{item.tipeArea}</TableCell>
                    <TableCell className="max-w-xs truncate" title={item.alamat}>
                      {item.alamat}
                    </TableCell>
                    <TableCell>{item.luasArea}</TableCell>
                    <TableCell>{item.geometri}</TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteGeofence(item.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Menampilkan {filteredData.length === 0 ? '0' : '1'} sampai {filteredData.length} dari {geofenceData.length} entri
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            Previous
          </Button>
          <Button variant="outline" size="sm" className="bg-slate-800 text-white">
            1
          </Button>
          <Button variant="outline" size="sm" disabled>
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Geofence;
