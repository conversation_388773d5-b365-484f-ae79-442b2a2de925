import { useState, useEffect, useCallback } from 'react';

export interface MonitoringData {
  id: string;
  no: number;
  icon: string;
  noESeal: string;
  noIMEI: string;
  address: string; // Converted from lat/lng to actual street address
  posisiAltitude: string;
  dayaBaterai: string;
  dayaAki: string;
  event: string; // Human readable event (e.g., "Normal, tidak ada event", "Locked", etc.)
  eventCode: string; // Original event code (0, 1, 2, 3)
  idVendor: string;
  kecepatanKontainer: string;
  posisiLatitude: string;
  posisiLongitude: string;
  provinsi: string; // Converted from coordinates
  kota: string; // Converted from coordinates
  token: string;
  status: 'ONLINE' | 'OFFLINE';
  lockStatus: string;
  signalStrength: string;
  temperature: string;
  mileage: string;
  direction: string;
  lastUpdate?: string;
  trackingStatus: 'TRACKING' | 'IDLE';
  organization?: string;
}

export interface MonitoringPosition {
  id: string;
  noEseal: string;
  lat: number;
  lng: number;
  address: string;
  status: 'ONLINE' | 'OFFLINE';
  lockStatus: string;
  speed: string;
  lastUpdate?: string;
}

interface UseMonitoringDataProps {
  organizationSlug?: string;
  refreshInterval?: number; // in milliseconds, default 30 seconds
  autoRefresh?: boolean;
}

interface MonitoringDataResponse {
  success: boolean;
  data: MonitoringData[];
  total: number;
  timestamp: string;
}

interface PositionsResponse {
  success: boolean;
  data: MonitoringPosition[];
  total: number;
  timestamp: string;
}

export const useMonitoringData = ({
  organizationSlug,
  refreshInterval = 30000, // 30 seconds
  autoRefresh = true
}: UseMonitoringDataProps = {}) => {
  const [data, setData] = useState<MonitoringData[]>([]);
  const [positions, setPositions] = useState<MonitoringPosition[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const fetchMonitoringData = useCallback(async () => {
    try {
      setError(null);

      const params = new URLSearchParams();
      if (organizationSlug) {
        params.append('organizationSlug', organizationSlug);
      }

      const response = await fetch(`/api/monitoring/active-eseals?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: MonitoringDataResponse = await response.json();

      if (result.success) {
        setData(result.data);
        setLastUpdate(result.timestamp);
      } else {
        throw new Error('Failed to fetch monitoring data');
      }
    } catch (err) {
      console.error('Error fetching monitoring data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [organizationSlug]);

  const fetchPositions = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (organizationSlug) {
        params.append('organizationSlug', organizationSlug);
      }

      const response = await fetch(`/api/monitoring/real-time-positions?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: PositionsResponse = await response.json();

      if (result.success) {
        setPositions(result.data);
      } else {
        throw new Error('Failed to fetch position data');
      }
    } catch (err) {
      console.error('Error fetching position data:', err);
      // Don't set error for positions as it's secondary data
    }
  }, [organizationSlug]);

  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchMonitoringData(),
      fetchPositions()
    ]);
  }, [fetchMonitoringData, fetchPositions]);

  // Initial data fetch
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshData]);

  return {
    data,
    positions,
    loading,
    error,
    lastUpdate,
    refreshData,
    // Utility functions
    getESealById: (id: string) => data.find(eseal => eseal.id === id),
    getOnlineCount: () => data.filter(eseal => eseal.status === 'ONLINE').length,
    getTrackingCount: () => data.filter(eseal => eseal.trackingStatus === 'TRACKING').length,
    getTotalCount: () => data.length
  };
};

// Hook for individual E-Seal monitoring
export const useESealMonitoring = (esealId: string, refreshInterval = 10000) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchESealStatus = useCallback(async () => {
    if (!esealId) return;

    try {
      setError(null);

      const response = await fetch(`/api/monitoring/eseal/${esealId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        throw new Error('Failed to fetch E-Seal status');
      }
    } catch (err) {
      console.error('Error fetching E-Seal status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [esealId]);

  useEffect(() => {
    fetchESealStatus();
  }, [fetchESealStatus]);

  useEffect(() => {
    const interval = setInterval(fetchESealStatus, refreshInterval);
    return () => clearInterval(interval);
  }, [fetchESealStatus, refreshInterval]);

  return {
    data,
    loading,
    error,
    refresh: fetchESealStatus
  };
};
