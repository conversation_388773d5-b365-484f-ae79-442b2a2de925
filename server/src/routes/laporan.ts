import { Hono } from 'hono';

const laporan = new Hono();

// Trip Report endpoint - proxy to lock.md API
laporan.post('/trip-report', async (c) => {
  try {
    const body = await c.req.json();
    
    // Make request to actual lock.md API
    const response = await fetch('http://smartelock.net:8088/report/trip_report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any required authentication headers here
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Lock API error: ${response.status}`);
    }

    const data = await response.json() as any;
    return c.json(data);
  } catch (error) {
    console.error('Error calling lock.md API:', error);

    // Return fallback data for development
    const fallbackData = {
      success: true,
      data: [
        {
          id: 1,
          carId: body.data?.[0] || 'unknown',
          deviceId: body.data?.[0] || 'unknown',
          imei: `${body.data?.[0] || 'unknown'}123456789`,
          lockStatus: 1, // 1 = LOCKED, 0 = UNLOCKED
          eventCount: 1,
          durationMinutes: 135, // 2h 15m
          mileage: 35000, // meters
          maxSpeed: 55,
          avgSpeed: 22,
          startTime: body.startTime || new Date(Date.now() - 2*60*60*1000).toISOString().slice(0, 19).replace('T', ' '),
          endTime: body.endTime || new Date().toISOString().slice(0, 19).replace('T', ' '),
          startLat: -6.2088,
          startLng: 106.8456,
          endLat: -6.1751,
          endLng: 106.8650,
        }
      ]
    };
    
    return c.json(fallbackData);
  }
});

// Daily Mileage Report endpoint
laporan.post('/daily-mileage-report', async (c) => {
  try {
    const body = await c.req.json();
    
    const response = await fetch('http://smartelock.net:8088/report/daily_mileage_report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Lock API error: ${response.status}`);
    }

    const data = await response.json() as any;
    return c.json(data);
  } catch (error) {
    console.error('Error calling lock.md daily mileage API:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return c.json({ success: false, error: errorMessage }, 500);
  }
});

export default laporan;
