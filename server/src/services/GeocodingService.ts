import { PrismaClient } from '@prisma/client';

export interface AddressComponents {
  street?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
  formattedAddress: string;
}

export class GeocodingService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Convert latitude and longitude to address using OpenStreetMap Nominatim API
   * This is free and doesn't require API keys
   */
  async reverseGeocode(lat: string | number, lng: string | number): Promise<AddressComponents> {
    try {
      const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
      const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;

      // Validate coordinates
      if (isNaN(latitude) || isNaN(longitude)) {
        throw new Error('Invalid coordinates');
      }

      // Use OpenStreetMap Nominatim API for reverse geocoding
      const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1&accept-language=id`;

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'E-Seal-Monitor/1.0 (<EMAIL>)' // Required by Nominatim
        }
      });

      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }

      const data = await response.json();

      if (!data || data.error) {
        throw new Error('No address found for coordinates');
      }

      // Extract address components
      const address = data.address || {};

      // Build street address
      let street = '';
      if (address.house_number) street += address.house_number + ' ';
      if (address.road) street += address.road;
      else if (address.street) street += address.street;
      else if (address.pedestrian) street += address.pedestrian;
      else if (address.path) street += address.path;

      // Get city/town with more fallback options
      let city = address.city || address.town || address.village || address.municipality ||
                 address.county || address.suburb || address.district || address.subdistrict || '';

      // Get province/state with more fallback options
      let province = address.state || address.province || address.region ||
                     address['ISO3166-2-lvl4'] || address.state_district || '';

      // Special handling for Jakarta coordinates
      if (this.isJakartaCoordinates(latitude, longitude)) {
        province = 'DKI Jakarta';
        // If city is not set but we know it's Jakarta, try to get the area
        if (!city) {
          city = address.suburb || address.district || address.subdistrict || 'Jakarta';
        }
      }

      // Map common Indonesian province names
      province = this.mapIndonesianProvince(province);
      city = this.mapIndonesianCity(city, province);

      // Build formatted address
      let formattedAddress = data.display_name || '';

      // If we have Indonesian components, build a cleaner address
      if (street || city || province) {
        const parts = [];
        if (street) parts.push(street);
        if (city) parts.push(city);
        if (province) parts.push(province);
        if (parts.length > 0) {
          formattedAddress = parts.join(', ');
        }
      }

      // Clean up the formatted address to remove redundant information
      if (formattedAddress) {
        // Remove coordinates if they appear in the address
        formattedAddress = formattedAddress.replace(/[-+]?\d*\.?\d+,\s*[-+]?\d*\.?\d+/g, '').trim();
        // Remove leading/trailing commas
        formattedAddress = formattedAddress.replace(/^,\s*|,\s*$/g, '').trim();
        // Replace multiple commas with single comma
        formattedAddress = formattedAddress.replace(/,\s*,/g, ',').trim();
      }

      return {
        street: street || undefined,
        city: city || undefined,
        province: province || undefined,
        country: address.country || 'Indonesia',
        postalCode: address.postcode || undefined,
        formattedAddress: formattedAddress || `${latitude}, ${longitude}`
      };

    } catch (error) {
      console.error('Reverse geocoding error:', error);

      // Return fallback address
      return {
        formattedAddress: `${lat}, ${lng}`,
        city: undefined,
        province: undefined
      };
    }
  }

  /**
   * Map English province names to Indonesian names
   */
  private mapIndonesianProvince(province: string): string {
    if (!province) return '';

    const provinceMap: { [key: string]: string } = {
      'Jakarta': 'DKI Jakarta',
      'Special Capital Region of Jakarta': 'DKI Jakarta',
      'Daerah Khusus Ibukota Jakarta': 'DKI Jakarta',
      'West Java': 'Jawa Barat',
      'Central Java': 'Jawa Tengah',
      'East Java': 'Jawa Timur',
      'Banten': 'Banten',
      'Yogyakarta': 'DI Yogyakarta',
      'Special Region of Yogyakarta': 'DI Yogyakarta',
      'North Sumatra': 'Sumatera Utara',
      'West Sumatra': 'Sumatera Barat',
      'South Sumatra': 'Sumatera Selatan',
      'Riau': 'Riau',
      'Riau Islands': 'Kepulauan Riau',
      'Jambi': 'Jambi',
      'Bengkulu': 'Bengkulu',
      'Lampung': 'Lampung',
      'Bangka Belitung Islands': 'Kepulauan Bangka Belitung',
      'West Kalimantan': 'Kalimantan Barat',
      'Central Kalimantan': 'Kalimantan Tengah',
      'South Kalimantan': 'Kalimantan Selatan',
      'East Kalimantan': 'Kalimantan Timur',
      'North Kalimantan': 'Kalimantan Utara',
      'North Sulawesi': 'Sulawesi Utara',
      'Central Sulawesi': 'Sulawesi Tengah',
      'South Sulawesi': 'Sulawesi Selatan',
      'Southeast Sulawesi': 'Sulawesi Tenggara',
      'Gorontalo': 'Gorontalo',
      'West Sulawesi': 'Sulawesi Barat',
      'Bali': 'Bali',
      'West Nusa Tenggara': 'Nusa Tenggara Barat',
      'East Nusa Tenggara': 'Nusa Tenggara Timur',
      'Maluku': 'Maluku',
      'North Maluku': 'Maluku Utara',
      'Papua': 'Papua',
      'West Papua': 'Papua Barat',
      'Central Papua': 'Papua Tengah',
      'Highland Papua': 'Papua Pegunungan',
      'South Papua': 'Papua Selatan',
      'Southwest Papua': 'Papua Barat Daya'
    };

    return provinceMap[province] || province;
  }

  /**
   * Check if coordinates are within Jakarta area
   */
  private isJakartaCoordinates(lat: number, lng: number): boolean {
    // Jakarta bounding box (approximate)
    // North: -6.0744, South: -6.3744, West: 106.6922, East: 107.0361
    return lat >= -6.3744 && lat <= -6.0744 && lng >= 106.6922 && lng <= 107.0361;
  }

  /**
   * Map and clean city names
   */
  private mapIndonesianCity(city: string, _province: string): string {
    if (!city) return '';

    // Remove common prefixes/suffixes
    city = city.replace(/^(Kota|Kabupaten|Kab\.|Kec\.|Kelurahan|Desa)\s+/i, '');
    city = city.replace(/\s+(City|Regency|District)$/i, '');

    // Capitalize properly
    city = city.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');

    return city;
  }

  /**
   * Batch reverse geocode multiple coordinates
   */
  async batchReverseGeocode(coordinates: Array<{ lat: string | number; lng: string | number; id?: string }>): Promise<Array<AddressComponents & { id?: string }>> {
    const results = [];

    for (const coord of coordinates) {
      try {
        // Add delay to respect rate limits (1 request per second for Nominatim)
        if (results.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        const address = await this.reverseGeocode(coord.lat, coord.lng);
        results.push({
          ...address,
          id: coord.id
        });
      } catch (error) {
        console.error(`Failed to geocode ${coord.lat}, ${coord.lng}:`, error);
        results.push({
          formattedAddress: `${coord.lat}, ${coord.lng}`,
          id: coord.id
        });
      }
    }

    return results;
  }
}
